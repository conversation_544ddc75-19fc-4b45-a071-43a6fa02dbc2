import { toNano } from '@ton/core';
import { useTonConnectUI } from '@tonconnect/ui-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

import { useRootContext } from '@/root-context';

export function useDepositValidation() {
  const { appConfig } = useRootContext();

  const validateAmount = useCallback(
    (amount: string): boolean => {
      if (!amount || !appConfig) return false;

      const numAmount = parseFloat(amount);
      if (isNaN(numAmount) || numAmount <= 0) return false;

      return numAmount >= appConfig.minDepositAmount;
    },
    [appConfig],
  );

  return { validateAmount, appConfig };
}

export function useDepositTransaction() {
  const [tonConnectUI] = useTonConnectUI();
  const { appConfig, refetchUser } = useRootContext();
  const [loading, setLoading] = useState(false);

  const executeDeposit = useCallback(
    async (amount: string) => {
      if (!appConfig) {
        toast.error('Configuration not loaded');
        return false;
      }

      if (!tonConnectUI.account?.address) {
        toast.error('Please connect your wallet first');
        return false;
      }

      const marketplaceWallet =
        process.env.NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS;
      if (!marketplaceWallet) {
        toast.error('Marketplace wallet not configured');
        return false;
      }

      try {
        setLoading(true);

        const numAmount = parseFloat(amount);
        const totalAmount = numAmount + appConfig.depositFee;

        const transaction = {
          validUntil: Math.floor(Date.now() / 1000) + 300,
          messages: [
            {
              address: marketplaceWallet,
              amount: toNano(totalAmount.toString()).toString(),
              payload: '',
            },
          ],
        };

        console.log('Sending transaction:', transaction);
        const result = await tonConnectUI.sendTransaction(transaction);
        console.log('Transaction result:', result);

        toast.success(`Deposit of ${numAmount} TON initiated successfully!`);
        return true;
      } catch (error) {
        console.error('Deposit failed:', error);
        toast.error('Deposit failed. Please try again.');
        return false;
      } finally {
        setLoading(false);
      }
    },
    [tonConnectUI, appConfig],
  );

  const refetchUserData = useCallback(async () => {
    try {
      await refetchUser();
      toast.success('Balance updated successfully!');
    } catch (error) {
      console.error('Failed to refetch user:', error);
      toast.error('Failed to update balance');
    }
  }, [refetchUser]);

  return {
    executeDeposit,
    refetchUserData,
    loading,
    isWalletConnected: !!tonConnectUI.account?.address,
  };
}

export function useDepositState() {
  const [depositAmount, setDepositAmount] = useState('');
  const [showCountdownPopup, setShowCountdownPopup] = useState(false);

  const resetForm = useCallback(() => {
    setDepositAmount('');
  }, []);

  const openCountdownPopup = useCallback(() => {
    setShowCountdownPopup(true);
  }, []);

  const closeCountdownPopup = useCallback(() => {
    setShowCountdownPopup(false);
  }, []);

  return {
    depositAmount,
    setDepositAmount,
    showCountdownPopup,
    resetForm,
    openCountdownPopup,
    closeCountdownPopup,
  };
}
